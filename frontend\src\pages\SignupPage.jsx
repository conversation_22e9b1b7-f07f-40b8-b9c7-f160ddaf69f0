import React from 'react'
import { useState } from 'react';
import {useAuthStore}  from '../store/useAuthStore.js';
import { MessageSquare,User,Mail,Lock, Eye, EyeOff } from 'lucide-react';

const SignupPage = () => {
  const[showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
  });

  const [signup, setSigningUp] = useAuthStore((state) => [state.signup, state.isSigningUp]);

  const validateForm = ()=>{}

  const handleSubmit = (e) => {
    e.preventDefault();
  };


  return (
    <div className=' min-h-screen grid lg:grid-cols-2'>
      {/*leftside*/}
     <div className="flex flex-col justify-center items-center p-6 sm:p-12">
      <div className="w-full max-w-sm space-y-8">
        {/*logo*/}
        <div className="text-center mb-8">
         <div className="flex flex-col items-center gap-2 group">
          <div className="size-10 rounded-xl bg-primary/10 flex justify-center items-center group-hover:bg-primary/20 transition-colors">
          <MessageSquare className="size-6 text-primary"/>
          </div>
          <h1 className="text-2xl font-bold mt-2">Create Account</h1>
          <p className='text-base-content/60'>Get started with your free account</p>
         </div>
        </div>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="form-control">
            <label className="label">
              <span className="label-text font-bold">Full Name</span>
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <User className="size-4 text-base-content/40"/>
              </div>
              <input
                type="text"
                value={formData.fullName}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                placeholder="Enter your full name"
                className="input input-bordered w-full pl-10"
              />
            </div>
          </div>

        </form>
      </div>
     </div>
    </div>
  )
}

export default SignupPage