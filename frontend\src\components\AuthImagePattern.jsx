const AuthImagePattern = ({title , subtitle}) => {
  return (
    <div className="hidden lg:block items-center justify-center bg-base-200 p-12">
      <div className=" max-w-md text-center">
        <div className="grid grid-cols-3 gap-4 mb-8">
            {[...Array(9)].map((_, index) => (
              <div
                key={index}
                className={`aspect-square rounded-2xl bg-primary/10${
                    index %2 === 0 ? "animate-pulse" : ""
                }`}
              />
            ))}
        </div>
      </div>
    </div>
  );
};

export default AuthImagePattern;
